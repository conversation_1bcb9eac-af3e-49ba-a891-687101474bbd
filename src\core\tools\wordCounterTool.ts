import { ToolDefinition } from "@core/prompts/model_prompts/jsonToolToXml"

/**
 * 判断字符是否为中文汉字
 */
function isChineseChar(char: string): boolean {
  const code = char.charCodeAt(0);
  return (
    (code >= 0x4e00 && code <= 0x9fff) ||  // 基本汉字
    (code >= 0x3400 && code <= 0x4dbf) ||  // 扩展A
    (code >= 0x20000 && code <= 0x2a6df) || // 扩展B
    (code >= 0x2a700 && code <= 0x2b73f) || // 扩展C
    (code >= 0x2b740 && code <= 0x2b81f) || // 扩展D
    (code >= 0x2b820 && code <= 0x2ceaf) || // 扩展E
    (code >= 0xf900 && code <= 0xfaff) ||   // 兼容汉字
    (code >= 0x2f800 && code <= 0x2fa1f)    // 兼容扩展
  );
}

/**
 * 判断字符是否为英文字符
 */
function isEnglishChar(char: string): boolean {
  const code = char.charCodeAt(0);
  return (code >= 0x41 && code <= 0x5a) || (code >= 0x61 && code <= 0x7a);
}

/**
 * 判断字符是否为全角字符
 */
function isFullWidthChar(char: string): boolean {
  const code = char.charCodeAt(0);
  return (
    (code >= 0xff01 && code <= 0xff5e) ||  // 全角ASCII
    (code >= 0x3000 && code <= 0x303f) ||  // CJK符号和标点
    (code >= 0x2e80 && code <= 0x2eff) ||  // CJK部首补充
    (code >= 0x2f00 && code <= 0x2fdf) ||  // 康熙部首
    (code >= 0x31c0 && code <= 0x31ef) ||  // CJK笔画
    (code >= 0x3200 && code <= 0x32ff) ||  // 带圈CJK字母和月份
    (code >= 0x3300 && code <= 0x33ff) ||  // CJK兼容
    (code >= 0xfe30 && code <= 0xfe4f)     // CJK兼容形式
  );
}

/**
 * 判断字符是否为半角字符
 */
function isHalfWidthChar(char: string): boolean {
  const code = char.charCodeAt(0);
  return (
    (code >= 0x20 && code <= 0x7e) ||      // 基本ASCII
    (code >= 0xff61 && code <= 0xff9f)     // 半角片假名
  );
}

/**
 * 统计字符类型
 */
function countCharacters(content: string) {
  let chineseChars = 0;
  let englishChars = 0;
  let fullWidthChars = 0;
  let halfWidthChars = 0;
  let otherChars = 0;

  for (const char of content) {
    if (isChineseChar(char)) {
      chineseChars++;
    } else if (isEnglishChar(char)) {
      englishChars++;
    } else if (isFullWidthChar(char)) {
      fullWidthChars++;
    } else if (isHalfWidthChar(char)) {
      halfWidthChars++;
    } else {
      otherChars++;
    }
  }

  return {
    chineseChars,
    englishChars,
    fullWidthChars,
    halfWidthChars,
    otherChars
  };
}

/**
 * 计算字数
 */
function calculateWordCount(stats: ReturnType<typeof countCharacters>): number {
  const { chineseChars, englishChars, fullWidthChars, halfWidthChars } = stats;
  
  // 计算字数：汉字=1，英文=0.5，全角=1，半角=0.5
  const rawWordCount = chineseChars * 1 + englishChars * 0.5 + fullWidthChars * 1 + halfWidthChars * 0.5;
  
  // 向上取整
  return Math.ceil(rawWordCount);
}

/**
 * 字数统计结果接口
 */
export interface WordCountResult {
  /** 文件路径 */
  filePath: string;
  /** 总字数（向上取整） */
  totalWords: number;
  /** 详细统计信息 */
  details: {
    /** 汉字数量 */
    chineseChars: number;
    /** 英文字符数量 */
    englishChars: number;
    /** 全角字符数量 */
    fullWidthChars: number;
    /** 半角字符数量 */
    halfWidthChars: number;
    /** 其他字符数量 */
    otherChars: number;
    /** 原始字数（未取整） */
    rawWordCount: number;
  };
  /** 是否成功读取文件 */
  success: boolean;
  /** 错误信息（如果有） */
  error?: string;
}

/**
 * 统计单个文件的字数
 */
export async function countWordsInFile(filePath: string, content: string): Promise<WordCountResult> {
  try {
    const stats = countCharacters(content);
    const rawWordCount = stats.chineseChars * 1 + stats.englishChars * 0.5 + stats.fullWidthChars * 1 + stats.halfWidthChars * 0.5;
    const totalWords = Math.ceil(rawWordCount);

    return {
      filePath,
      totalWords,
      details: {
        ...stats,
        rawWordCount
      },
      success: true
    };
  } catch (error) {
    return {
      filePath,
      totalWords: 0,
      details: {
        chineseChars: 0,
        englishChars: 0,
        fullWidthChars: 0,
        halfWidthChars: 0,
        otherChars: 0,
        rawWordCount: 0
      },
      success: false,
      error: error instanceof Error ? error.message : '未知错误'
    };
  }
}

/**
 * 统计多个文件的字数
 */
export async function countWordsInFiles(filePaths: string[], fileContents: string[]): Promise<{
  results: WordCountResult[];
  totalWords: number;
  summary: string;
}> {
  const results: WordCountResult[] = [];
  let totalWords = 0;

  for (let i = 0; i < filePaths.length; i++) {
    const result = await countWordsInFile(filePaths[i], fileContents[i]);
    results.push(result);
    if (result.success) {
      totalWords += result.totalWords;
    }
  }

  // 生成汇总信息
  const successCount = results.filter(r => r.success).length;
  const failureCount = results.length - successCount;
  
  let summary = `字数统计完成！\n\n`;
  
  results.forEach(result => {
    if (result.success) {
      summary += `📄 ${result.filePath}:\n`;
      summary += `  - 汉字: ${result.details.chineseChars}\n`;
      summary += `  - 英文字符: ${result.details.englishChars}\n`;
      summary += `  - 全角字符: ${result.details.fullWidthChars}\n`;
      summary += `  - 半角字符: ${result.details.halfWidthChars}\n`;
      summary += `  - 其他字符: ${result.details.otherChars}\n`;
      summary += `  - 原始字数: ${result.details.rawWordCount.toFixed(1)}\n`;
      summary += `  - 总字数: ${result.totalWords}\n\n`;
    } else {
      summary += `❌ ${result.filePath}: ${result.error}\n\n`;
    }
  });
  
  summary += `📊 汇总:\n`;
  summary += `  - 成功处理: ${successCount} 个文件\n`;
  if (failureCount > 0) {
    summary += `  - 处理失败: ${failureCount} 个文件\n`;
  }
  summary += `  - 总计字数: ${totalWords}`;

  return {
    results,
    totalWords,
    summary
  };
}

const descriptionForAgent = `Request to count words in specified files using Chinese character counting rules. This tool analyzes text content and provides detailed statistics for different character types. Counting rules: Chinese characters = 1.0, English characters = 0.5, full-width characters = 1.0, half-width characters = 0.5. The final word count is rounded up using Math.ceil().`

export const wordCounterToolDefinition = (cwd: string): ToolDefinition => ({
  name: "count_words",
  descriptionForAgent,
  inputSchema: {
    type: "object",
    properties: {
      filePaths: {
        type: "array",
        items: {
          type: "string"
        },
        description: `Array of file paths to count words for (relative to the current working directory ${cwd.toPosix()})`
      }
    },
    required: ["filePaths"]
  }
})
